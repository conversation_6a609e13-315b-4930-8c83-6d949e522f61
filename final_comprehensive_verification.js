// التحقق النهائي الشامل من جميع إصلاحات أسماء الصور في نظام Bug Bounty v4.0

const fs = require('fs');

console.log('🔍 التحقق النهائي الشامل من جميع الإصلاحات...');

// قراءة الملفات
const bugBountyCore = fs.readFileSync('./assets/modules/bugbounty/BugBountyCore.js', 'utf8');
const pythonBridge = fs.readFileSync('./assets/modules/bugbounty/python_screenshot_bridge.js', 'utf8');
const pythonService = fs.readFileSync('./assets/modules/bugbounty/python_web_service.py', 'utf8');

console.log('\n📋 نتائج التحقق الشامل:');
console.log('==========================');

let allFixed = true;

// 1. التحقق من عدم وجود أنماط خاطئة في BugBountyCore.js
console.log('\n🔍 1. فحص BugBountyCore.js:');

const wrongPatterns = [
    /\.name\.replace\(\/\\s\+\/g,\s*'_'\)/g,
    /vulnName\.replace\(\/\\s\+\/g,\s*'_'\)/g,
    /vulnerability\.name\.replace\(\/\\s\+\/g,\s*'_'\)/g
];

wrongPatterns.forEach((pattern, index) => {
    const matches = bugBountyCore.match(pattern);
    if (matches && matches.length > 0) {
        console.log(`❌ وجد ${matches.length} نمط خاطئ ${index + 1}`);
        allFixed = false;
    } else {
        console.log(`✅ لا توجد أنماط خاطئة ${index + 1}`);
    }
});

// 2. التحقق من استخدام getCleanVulnerabilityName
const getCleanVulnMatches = bugBountyCore.match(/getCleanVulnerabilityName/g);
const getCleanVulnCount = getCleanVulnMatches ? getCleanVulnMatches.length : 0;
console.log(`✅ استخدامات getCleanVulnerabilityName: ${getCleanVulnCount}`);

if (getCleanVulnCount < 40) {
    console.log(`⚠️ عدد الاستخدامات قليل، متوقع 40+`);
    allFixed = false;
}

// 3. التحقق من Python Bridge
console.log('\n🔍 2. فحص Python Bridge:');

if (pythonBridge.includes('filename: args[1]')) {
    console.log('✅ Python Bridge يستخدم filename بدلاً من screenshot_id');
} else if (pythonBridge.includes('screenshot_id: args[1]')) {
    console.log('❌ Python Bridge لا يزال يستخدم screenshot_id');
    allFixed = false;
} else {
    console.log('⚠️ لم يتم العثور على النمط في Python Bridge');
}

// 4. التحقق من Python Service
console.log('\n🔍 3. فحص Python Service:');

if (pythonService.includes('capture_screenshot_with_filename')) {
    console.log('✅ Python Service يحتوي على capture_screenshot_with_filename');
} else {
    console.log('❌ Python Service لا يحتوي على capture_screenshot_with_filename');
    allFixed = false;
}

if (pythonService.includes('filename = data.get(\'filename\'')) {
    console.log('✅ Python Service يستخدم filename من البيانات');
} else {
    console.log('❌ Python Service لا يستخدم filename من البيانات');
    allFixed = false;
}

// 5. التحقق من دالة captureAfterExploitationScreenshot
console.log('\n🔍 4. فحص دالة captureAfterExploitationScreenshot:');

if (bugBountyCore.includes('captureAfterExploitationScreenshot')) {
    console.log('✅ دالة captureAfterExploitationScreenshot موجودة');
    
    // التحقق من payloads
    const payloads = [
        'modifiedUrl.*sql.*OR',
        'modifiedUrl.*xss.*script',
        'modifiedUrl.*api.*docs',
        'modifiedUrl.*directory.*passwd',
        'modifiedUrl.*command.*whoami'
    ];
    
    let payloadCount = 0;
    payloads.forEach(payload => {
        const regex = new RegExp(payload, 'i');
        if (regex.test(bugBountyCore)) {
            payloadCount++;
        }
    });
    
    console.log(`✅ وجد ${payloadCount}/5 payloads للثغرات المختلفة`);
    if (payloadCount < 3) {
        console.log('⚠️ عدد payloads قليل');
        allFixed = false;
    }
} else {
    console.log('❌ دالة captureAfterExploitationScreenshot غير موجودة');
    allFixed = false;
}

// 6. التحقق من أمثلة مسارات الصور
console.log('\n🔍 5. فحص مسارات الصور:');

const imagePathExamples = [
    'before_${this.getCleanVulnerabilityName',
    'during_${this.getCleanVulnerabilityName',
    'after_${this.getCleanVulnerabilityName'
];

imagePathExamples.forEach((example, index) => {
    const stages = ['before', 'during', 'after'];
    const matches = bugBountyCore.match(new RegExp(example.replace('${', '\\${'), 'g'));
    const count = matches ? matches.length : 0;
    console.log(`✅ مسارات ${stages[index]}: ${count} استخدام`);
    
    if (count < 5) {
        console.log(`⚠️ عدد استخدامات ${stages[index]} قليل`);
    }
});

// 7. التحقق من عدم وجود أسماء ملفات خاطئة
console.log('\n🔍 6. فحص أسماء الملفات الخاطئة:');

const wrongFilePatterns = [
    /screenshot_.*testphp.*timestamp/g,
    /before_.*timestamp.*\.png/g,
    /during_.*timestamp.*\.png/g,
    /after_.*timestamp.*\.png/g
];

let wrongFileCount = 0;
wrongFilePatterns.forEach((pattern, index) => {
    const matches = bugBountyCore.match(pattern);
    if (matches && matches.length > 0) {
        console.log(`❌ وجد ${matches.length} اسم ملف خاطئ نوع ${index + 1}`);
        wrongFileCount += matches.length;
        allFixed = false;
    }
});

if (wrongFileCount === 0) {
    console.log('✅ لا توجد أسماء ملفات خاطئة');
}

// 8. النتيجة النهائية
console.log('\n🎯 النتيجة النهائية:');
console.log('==================');

if (allFixed) {
    console.log('✅ ممتاز! جميع الإصلاحات مطبقة بنجاح');
    console.log('✅ BugBountyCore.js: جميع الأنماط صحيحة');
    console.log('✅ Python Bridge: يستخدم filename');
    console.log('✅ Python Service: يحتوي على الدوال المطلوبة');
    console.log('✅ صور بعد الاستغلال: تحتوي على payloads حقيقية');
    console.log('✅ مسارات الصور: تستخدم getCleanVulnerabilityName');
    
    console.log('\n🎯 النظام جاهز لإنتاج:');
    console.log('📁 مجلدات بأسماء الروابط: testphp_vulnweb_com');
    console.log('📸 صور بأسماء صحيحة: before_API_Documentation_Exposure.png');
    console.log('🔥 صور حقيقية بعد الاستغلال مع payloads فعلية');
    
} else {
    console.log('❌ توجد مشاكل تحتاج إصلاح إضافي');
    console.log('❌ راجع التفاصيل أعلاه لمعرفة المشاكل');
}

console.log('\n📊 إحصائيات:');
console.log(`📄 BugBountyCore.js: ${bugBountyCore.split('\n').length} سطر`);
console.log(`🔧 استخدامات getCleanVulnerabilityName: ${getCleanVulnCount}`);
console.log(`🐍 Python Bridge: ${pythonBridge.split('\n').length} سطر`);
console.log(`🐍 Python Service: ${pythonService.split('\n').length} سطر`);

console.log('\n🔥 الخطوات التالية:');
if (allFixed) {
    console.log('1. ✅ تشغيل النظام لاختبار الإصلاحات');
    console.log('2. ✅ التحقق من إنتاج الصور بالأسماء الصحيحة');
    console.log('3. ✅ التحقق من عرض الصور في التقارير');
    console.log('4. ✅ التحقق من صور بعد الاستغلال الحقيقية');
} else {
    console.log('1. ❌ إصلاح المشاكل المتبقية');
    console.log('2. ❌ إعادة تشغيل هذا الاختبار');
    console.log('3. ❌ اختبار النظام بعد الإصلاح');
}
