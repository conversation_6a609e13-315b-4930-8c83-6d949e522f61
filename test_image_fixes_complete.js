// اختبار شامل للتحقق من إصلاح أسماء الصور في نظام Bug Bounty v4.0

console.log('🧪 اختبار شامل لإصلاحات أسماء الصور...');

// محاكاة BugBountyCore
class TestBugBountyCore {
    getCleanVulnerabilityName(vulnerability) {
        const vulnName = vulnerability.name || vulnerability.type || 'Unknown_Vulnerability';
        
        // تنظيف اسم الثغرة وتحويله للصيغة الصحيحة
        let cleanName = vulnName.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_');
        
        // إزالة التكرار إذا وجد
        if (cleanName.includes('_')) {
            const parts = cleanName.split('_');
            const uniqueParts = [...new Set(parts)];
            cleanName = uniqueParts.join('_');
        }

        // تطبيق تحويلات خاصة لأسماء الثغرات الشائعة
        const nameMapping = {
            'API_Documentation_Exposure': 'API_Documentation_Exposure',
            'SQL_Injection': 'SQL_Injection',
            'XSS_Cross_Site_Scripting': 'XSS_Cross_Site_Scripting',
            'XSS': 'XSS_Cross_Site_Scripting',
            'CSRF_Cross_Site_Request_Forgery': 'CSRF_Cross_Site_Request_Forgery',
            'CSRF': 'CSRF_Cross_Site_Request_Forgery',
            'Command_Injection': 'Command_Injection',
            'File_Upload_Vulnerability': 'File_Upload_Vulnerability',
            'Directory_Traversal': 'Directory_Traversal',
            'Path_Traversal': 'Directory_Traversal',
            'Authentication_Bypass': 'Authentication_Bypass',
            'Session_Management_Vulnerability': 'Session_Management_Vulnerability',
            'Information_Disclosure': 'Information_Disclosure'
        };

        return nameMapping[cleanName] || cleanName;
    }

    getCorrectFolderName(vulnerability) {
        // إنشاء اسم المجلد بناءً على الرابط (الأولوية لاسم الرابط)
        const targetUrl = vulnerability.url || vulnerability.target_url || vulnerability.location || 'http://testphp.vulnweb.com';
        const cleanUrl = targetUrl.replace(/https?:\/\//, '').replace(/[\/\?&=\.]/g, '_').replace(/[<>:"|*]/g, '');
        return cleanUrl;
    }

    getCorrectImageName(stage, vulnerability) {
        const cleanVulnName = this.getCleanVulnerabilityName(vulnerability);
        return `${stage}_${cleanVulnName}.png`;
    }

    getCorrectImagePath(vulnerability, stage) {
        const folderName = this.getCorrectFolderName(vulnerability);
        const imageName = this.getCorrectImageName(stage, vulnerability);
        return `./assets/modules/bugbounty/screenshots/${folderName}/${imageName}`;
    }
}

// اختبار الحالات المختلفة
const testCore = new TestBugBountyCore();

const testCases = [
    {
        name: 'API Documentation Exposure',
        url: 'http://testphp.vulnweb.com',
        expected: {
            folder: 'testphp_vulnweb_com',
            cleanName: 'API_Documentation_Exposure',
            beforeImage: 'before_API_Documentation_Exposure.png',
            duringImage: 'during_API_Documentation_Exposure.png',
            afterImage: 'after_API_Documentation_Exposure.png',
            beforePath: './assets/modules/bugbounty/screenshots/testphp_vulnweb_com/before_API_Documentation_Exposure.png',
            duringPath: './assets/modules/bugbounty/screenshots/testphp_vulnweb_com/during_API_Documentation_Exposure.png',
            afterPath: './assets/modules/bugbounty/screenshots/testphp_vulnweb_com/after_API_Documentation_Exposure.png'
        }
    },
    {
        name: 'SQL Injection',
        url: 'http://testphp.vulnweb.com/artists.php',
        expected: {
            folder: 'testphp_vulnweb_com',
            cleanName: 'SQL_Injection',
            beforeImage: 'before_SQL_Injection.png',
            duringImage: 'during_SQL_Injection.png',
            afterImage: 'after_SQL_Injection.png',
            beforePath: './assets/modules/bugbounty/screenshots/testphp_vulnweb_com/before_SQL_Injection.png',
            duringPath: './assets/modules/bugbounty/screenshots/testphp_vulnweb_com/during_SQL_Injection.png',
            afterPath: './assets/modules/bugbounty/screenshots/testphp_vulnweb_com/after_SQL_Injection.png'
        }
    }
];

console.log('\n📋 نتائج الاختبار الشامل:');
console.log('==========================');

let allTestsPassed = true;

testCases.forEach((testCase, index) => {
    console.log(`\n🧪 اختبار ${index + 1}: ${testCase.name}`);
    
    const vulnerability = { name: testCase.name, url: testCase.url };
    
    // اختبار اسم المجلد
    const folderName = testCore.getCorrectFolderName(vulnerability);
    const folderTest = folderName === testCase.expected.folder;
    console.log(`📁 اسم المجلد: ${folderName} ${folderTest ? '✅' : '❌'}`);
    if (!folderTest) allTestsPassed = false;
    
    // اختبار اسم الثغرة المنظف
    const cleanName = testCore.getCleanVulnerabilityName(vulnerability);
    const cleanNameTest = cleanName === testCase.expected.cleanName;
    console.log(`🏷️ اسم الثغرة المنظف: ${cleanName} ${cleanNameTest ? '✅' : '❌'}`);
    if (!cleanNameTest) allTestsPassed = false;
    
    // اختبار أسماء الصور
    const beforeImage = testCore.getCorrectImageName('before', vulnerability);
    const duringImage = testCore.getCorrectImageName('during', vulnerability);
    const afterImage = testCore.getCorrectImageName('after', vulnerability);
    
    const beforeTest = beforeImage === testCase.expected.beforeImage;
    const duringTest = duringImage === testCase.expected.duringImage;
    const afterTest = afterImage === testCase.expected.afterImage;
    
    console.log(`📸 صورة قبل: ${beforeImage} ${beforeTest ? '✅' : '❌'}`);
    console.log(`📸 صورة أثناء: ${duringImage} ${duringTest ? '✅' : '❌'}`);
    console.log(`📸 صورة بعد: ${afterImage} ${afterTest ? '✅' : '❌'}`);
    
    if (!beforeTest || !duringTest || !afterTest) allTestsPassed = false;
    
    // اختبار المسارات الكاملة
    const beforePath = testCore.getCorrectImagePath(vulnerability, 'before');
    const duringPath = testCore.getCorrectImagePath(vulnerability, 'during');
    const afterPath = testCore.getCorrectImagePath(vulnerability, 'after');
    
    const beforePathTest = beforePath === testCase.expected.beforePath;
    const duringPathTest = duringPath === testCase.expected.duringPath;
    const afterPathTest = afterPath === testCase.expected.afterPath;
    
    console.log(`🗂️ مسار قبل: ${beforePath} ${beforePathTest ? '✅' : '❌'}`);
    console.log(`🗂️ مسار أثناء: ${duringPath} ${duringPathTest ? '✅' : '❌'}`);
    console.log(`🗂️ مسار بعد: ${afterPath} ${afterPathTest ? '✅' : '❌'}`);
    
    if (!beforePathTest || !duringPathTest || !afterPathTest) allTestsPassed = false;
});

console.log('\n🎯 النتيجة النهائية:');
console.log('==================');
if (allTestsPassed) {
    console.log('✅ ممتاز! جميع الاختبارات نجحت!');
    console.log('✅ أسماء المجلدات صحيحة: اسم الرابط');
    console.log('✅ أسماء الصور صحيحة: stage_VulnerabilityName.png');
    console.log('✅ المسارات الكاملة صحيحة');
    console.log('✅ لا يتم استخدام اسم الموقع في اسم الصورة');
    
    console.log('\n📝 الآن يجب أن تعمل الصور في التقارير:');
    console.log('- before_API_Documentation_Exposure.png ✅');
    console.log('- during_API_Documentation_Exposure.png ✅');
    console.log('- after_API_Documentation_Exposure.png ✅');
    
} else {
    console.log('❌ بعض الاختبارات فشلت. يحتاج مراجعة إضافية.');
}

console.log('\n🔥 الخطوات التالية:');
console.log('1. تشغيل النظام لإنتاج صور جديدة');
console.log('2. التحقق من أن الصور تُحفظ بالأسماء الصحيحة');
console.log('3. التحقق من أن التقارير تعرض الصور بشكل صحيح');
console.log('4. التأكد من أن صور "بعد الاستغلال" حقيقية وليست ألوان');
