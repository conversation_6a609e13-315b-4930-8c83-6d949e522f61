// اختبار إصلاحات الصور وعرضها في التقارير

const fs = require('fs');
const path = require('path');

console.log('🔍 اختبار إصلاحات الصور وعرضها في التقارير...');
console.log('================================================');

// فحص الصور الموجودة
console.log('\n📁 فحص الصور الموجودة:');

const screenshotsPath = './assets/modules/bugbounty/screenshots/testphp_vulnweb_com';

try {
    const files = fs.readdirSync(screenshotsPath);
    
    // البحث عن الصور بأسماء صحيحة
    const correctImages = files.filter(file => 
        file.startsWith('before_') || 
        file.startsWith('during_') || 
        file.startsWith('after_')
    );
    
    console.log(`📊 إجمالي الملفات: ${files.length}`);
    console.log(`✅ صور بأسماء صحيحة: ${correctImages.length}`);
    
    if (correctImages.length > 0) {
        console.log('\n🎯 أحدث الصور بأسماء صحيحة:');
        
        // ترتيب الصور حسب تاريخ التعديل
        const sortedImages = correctImages
            .map(file => ({
                name: file,
                time: fs.statSync(path.join(screenshotsPath, file)).mtime,
                size: fs.statSync(path.join(screenshotsPath, file)).size
            }))
            .sort((a, b) => b.time - a.time)
            .slice(0, 10);
        
        sortedImages.forEach((img, index) => {
            const sizeKB = Math.round(img.size / 1024);
            const isLarge = img.size > 100000; // أكبر من 100KB
            const sizeStatus = isLarge ? '✅' : '⚠️';
            console.log(`   ${sizeStatus} ${img.name} (${sizeKB}KB) - ${img.time.toLocaleString('ar')}`);
        });
        
        // تحليل أنواع الثغرات
        console.log('\n🔍 تحليل أنواع الثغرات:');
        const vulnerabilityTypes = new Set();
        
        correctImages.forEach(img => {
            const parts = img.replace('.png', '').split('_');
            if (parts.length > 1) {
                const vulnType = parts.slice(1).join('_');
                vulnerabilityTypes.add(vulnType);
            }
        });
        
        console.log(`📋 أنواع الثغرات المكتشفة: ${vulnerabilityTypes.size}`);
        vulnerabilityTypes.forEach(type => {
            const beforeExists = correctImages.some(img => img === `before_${type}.png`);
            const duringExists = correctImages.some(img => img === `during_${type}.png`);
            const afterExists = correctImages.some(img => img === `after_${type}.png`);
            
            const beforeStatus = beforeExists ? '✅' : '❌';
            const duringStatus = duringExists ? '✅' : '❌';
            const afterStatus = afterExists ? '✅' : '❌';
            
            console.log(`   📌 ${type}:`);
            console.log(`      ${beforeStatus} Before | ${duringStatus} During | ${afterStatus} After`);
        });
        
        // فحص صور بعد الاستغلال
        console.log('\n🚨 فحص صور بعد الاستغلال:');
        const afterImages = correctImages.filter(img => img.startsWith('after_'));
        
        afterImages.forEach(img => {
            const imgPath = path.join(screenshotsPath, img);
            const stats = fs.statSync(imgPath);
            const sizeKB = Math.round(stats.size / 1024);
            
            // تحليل حجم الصورة للتنبؤ بنوعها
            let analysis = '';
            if (stats.size < 50000) {
                analysis = '⚠️ قد تكون ألوان (صغيرة)';
            } else if (stats.size > 200000) {
                analysis = '✅ صورة حقيقية (كبيرة)';
            } else {
                analysis = '🔍 حجم متوسط (تحتاج فحص)';
            }
            
            console.log(`   📸 ${img}: ${sizeKB}KB - ${analysis}`);
        });
        
    } else {
        console.log('❌ لا توجد صور بأسماء صحيحة');
    }
    
} catch (error) {
    console.log(`❌ لا يمكن قراءة مجلد الصور: ${error.message}`);
}

// اختبار دوال الأسماء
console.log('\n🧪 اختبار دوال الأسماء:');

// محاكاة ثغرات مختلفة
const testVulnerabilities = [
    { name: 'SQL Injection', type: 'SQL Injection' },
    { name: 'API Documentation Exposure', type: 'Information Disclosure' },
    { name: 'Brute Force Attack', type: 'Authentication' },
    { name: 'XSS Cross Site Scripting', type: 'XSS' }
];

// محاكاة دالة getCleanVulnerabilityName
function mockGetCleanVulnerabilityName(vulnerability) {
    const vulnName = vulnerability.name || vulnerability.type || 'Unknown_Vulnerability';
    let cleanName = vulnName.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_');
    
    const nameMapping = {
        'API_Documentation_Exposure': 'API_Documentation_Exposure',
        'SQL_Injection': 'SQL_Injection',
        'XSS_Cross_Site_Scripting': 'XSS_Cross_Site_Scripting',
        'Brute_Force_Attack': 'Brute_Force_Attack'
    };
    
    return nameMapping[cleanName] || cleanName;
}

// محاكاة دالة getCorrectImageName
function mockGetCorrectImageName(stage, vulnerability) {
    const cleanVulnName = mockGetCleanVulnerabilityName(vulnerability);
    return `${stage}_${cleanVulnName}.png`;
}

testVulnerabilities.forEach(vuln => {
    console.log(`\n📌 ${vuln.name}:`);
    console.log(`   🧹 اسم منظف: ${mockGetCleanVulnerabilityName(vuln)}`);
    console.log(`   📸 Before: ${mockGetCorrectImageName('before', vuln)}`);
    console.log(`   📸 During: ${mockGetCorrectImageName('during', vuln)}`);
    console.log(`   📸 After: ${mockGetCorrectImageName('after', vuln)}`);
});

// فحص التطابق مع الصور الموجودة
console.log('\n🔍 فحص التطابق مع الصور الموجودة:');

try {
    const files = fs.readdirSync(screenshotsPath);
    
    testVulnerabilities.forEach(vuln => {
        const expectedBefore = mockGetCorrectImageName('before', vuln);
        const expectedDuring = mockGetCorrectImageName('during', vuln);
        const expectedAfter = mockGetCorrectImageName('after', vuln);
        
        const beforeExists = files.includes(expectedBefore);
        const duringExists = files.includes(expectedDuring);
        const afterExists = files.includes(expectedAfter);
        
        const beforeStatus = beforeExists ? '✅' : '❌';
        const duringStatus = duringExists ? '✅' : '❌';
        const afterStatus = afterExists ? '✅' : '❌';
        
        console.log(`📌 ${vuln.name}:`);
        console.log(`   ${beforeStatus} ${expectedBefore}`);
        console.log(`   ${duringStatus} ${expectedDuring}`);
        console.log(`   ${afterStatus} ${expectedAfter}`);
    });
    
} catch (error) {
    console.log(`❌ خطأ في فحص التطابق: ${error.message}`);
}

console.log('\n📋 خلاصة النتائج:');
console.log('================');
console.log('✅ تم فحص الصور الموجودة');
console.log('✅ تم اختبار دوال الأسماء');
console.log('✅ تم فحص التطابق');
console.log('\n🎯 التوصيات:');
console.log('1. إذا كانت صور "after" صغيرة الحجم، فهي ألوان وتحتاج إصلاح');
console.log('2. إذا لم تتطابق أسماء الثغرات، فالتقرير لن يعرض الصور');
console.log('3. تأكد من أن الـ payloads تعمل مع الموقع المستهدف');

console.log('\n🚀 الاختبار مكتمل!');
