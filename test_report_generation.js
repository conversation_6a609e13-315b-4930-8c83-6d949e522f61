// اختبار إنتاج تقرير جديد لاختبار عرض الصور

const fs = require('fs');
const path = require('path');

console.log('📄 اختبار إنتاج تقرير جديد...');
console.log('================================');

// محاكاة بيانات Bug Bounty v4.0
const mockBugBountyData = {
    targetUrl: 'http://testphp.vulnweb.com',
    vulnerabilities: [
        {
            name: 'API Documentation Exposure',
            type: 'Information Disclosure',
            severity: 'Medium',
            description: 'API documentation is publicly accessible',
            impact: 'Information disclosure about API endpoints',
            recommendation: 'Restrict access to API documentation'
        }
    ],
    reportId: `testphp_vulnweb_com`,
    timestamp: new Date().toISOString()
};

// محاكاة دالة إنتاج التقرير
function generateMockReport() {
    const reportId = `Bug_Bounty_Page_1_testphp_vulnweb_com_${new Date().toISOString().replace(/[:.]/g, '').slice(0, 15)}`;
    
    console.log(`📋 إنتاج تقرير جديد: ${reportId}`);
    
    // محاكاة محتوى التقرير مع الصور الصحيحة
    const reportContent = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>Bug Bounty v4.0 Report - ${reportId}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .image-container { margin: 10px 0; }
        .image-container img { max-width: 100%; height: auto; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>🎯 Bug Bounty v4.0 Report</h1>
    <h2>📊 Target: ${mockBugBountyData.targetUrl}</h2>
    
    <div class="section">
        <h3>🔍 Vulnerabilities Found</h3>
        ${mockBugBountyData.vulnerabilities.map(vuln => `
            <div>
                <h4>📌 ${vuln.name}</h4>
                <p><strong>Type:</strong> ${vuln.type}</p>
                <p><strong>Severity:</strong> ${vuln.severity}</p>
                <p><strong>Description:</strong> ${vuln.description}</p>
            </div>
        `).join('')}
    </div>
    
    <div class="section">
        <h3>📸 Visual Evidence</h3>
        <p>📁 Screenshot Directory: assets/modules/bugbounty/screenshots/${mockBugBountyData.reportId}/</p>
        
        ${mockBugBountyData.vulnerabilities.map(vuln => {
            const cleanName = vuln.name.replace(/[^a-zA-Z0-9_]/g, '_');
            const beforeImg = `./assets/modules/bugbounty/screenshots/${mockBugBountyData.reportId}/before_${cleanName}.png`;
            const duringImg = `./assets/modules/bugbounty/screenshots/${mockBugBountyData.reportId}/during_${cleanName}.png`;
            const afterImg = `./assets/modules/bugbounty/screenshots/${mockBugBountyData.reportId}/after_${cleanName}.png`;
            
            return `
                <div class="vulnerability-images">
                    <h4>🎯 ${vuln.name} - Screenshots</h4>
                    
                    <div class="image-container">
                        <h5>🔒 Before Exploitation</h5>
                        <p>Expected file: ${beforeImg}</p>
                        ${fs.existsSync(beforeImg) ? 
                            `<img src="${beforeImg}" alt="Before ${vuln.name}" />
                             <p class="success">✅ Image found and loaded successfully!</p>` :
                            `<p class="error">❌ Image not found: ${beforeImg}</p>`
                        }
                    </div>
                    
                    <div class="image-container">
                        <h5>⚠️ During Exploitation</h5>
                        <p>Expected file: ${duringImg}</p>
                        ${fs.existsSync(duringImg) ? 
                            `<img src="${duringImg}" alt="During ${vuln.name}" />
                             <p class="success">✅ Image found and loaded successfully!</p>` :
                            `<p class="error">❌ Image not found: ${duringImg}</p>`
                        }
                    </div>
                    
                    <div class="image-container">
                        <h5>🚨 After Exploitation</h5>
                        <p>Expected file: ${afterImg}</p>
                        ${fs.existsSync(afterImg) ? 
                            `<img src="${afterImg}" alt="After ${vuln.name}" />
                             <p class="success">✅ Image found and loaded successfully!</p>` :
                            `<p class="error">❌ Image not found: ${afterImg}</p>`
                        }
                    </div>
                </div>
            `;
        }).join('')}
    </div>
    
    <div class="section">
        <h3>📋 Available Screenshots</h3>
        <p>🔍 Scanning directory: ./assets/modules/bugbounty/screenshots/${mockBugBountyData.reportId}/</p>
        ${(() => {
            try {
                const screenshotsPath = `./assets/modules/bugbounty/screenshots/${mockBugBountyData.reportId}`;
                const files = fs.readdirSync(screenshotsPath);
                
                return `
                    <p>📊 Total files found: ${files.length}</p>
                    <ul>
                        ${files.map(file => {
                            const isCorrect = file.startsWith('before_') || file.startsWith('during_') || file.startsWith('after_');
                            const status = isCorrect ? '✅' : '❌';
                            return `<li>${status} ${file}</li>`;
                        }).join('')}
                    </ul>
                `;
            } catch (error) {
                return `<p class="error">❌ Cannot read screenshots directory: ${error.message}</p>`;
            }
        })()}
    </div>
    
    <div class="section">
        <h3>🎯 Test Results Summary</h3>
        <p><strong>Report Generated:</strong> ${new Date().toLocaleString('ar')}</p>
        <p><strong>Target URL:</strong> ${mockBugBountyData.targetUrl}</p>
        <p><strong>Vulnerabilities:</strong> ${mockBugBountyData.vulnerabilities.length}</p>
        <p><strong>Expected Images:</strong> ${mockBugBountyData.vulnerabilities.length * 3} (before, during, after for each vulnerability)</p>
    </div>
    
</body>
</html>
    `;
    
    // حفظ التقرير
    const reportPath = `${reportId}.html`;
    fs.writeFileSync(reportPath, reportContent);
    
    console.log(`✅ تم إنتاج التقرير: ${reportPath}`);
    
    return { reportId, reportPath };
}

// تشغيل اختبار إنتاج التقرير
const result = generateMockReport();

console.log('\n📋 نتائج اختبار إنتاج التقرير:');
console.log('===============================');
console.log(`📄 اسم التقرير: ${result.reportId}`);
console.log(`📁 مسار التقرير: ${result.reportPath}`);

// فحص الصور المتوقعة
console.log('\n🔍 فحص الصور المتوقعة:');
const expectedImages = [
    'before_API_Documentation_Exposure.png',
    'during_API_Documentation_Exposure.png', 
    'after_API_Documentation_Exposure.png'
];

const screenshotsPath = './assets/modules/bugbounty/screenshots/testphp_vulnweb_com';

expectedImages.forEach(img => {
    const fullPath = path.join(screenshotsPath, img);
    const exists = fs.existsSync(fullPath);
    const status = exists ? '✅' : '❌';
    console.log(`${status} ${img} ${exists ? '(موجود)' : '(غير موجود)'}`);
});

console.log('\n🎯 خلاصة النتائج:');
console.log('================');
console.log('✅ تم إنتاج تقرير اختبار جديد بنجاح');
console.log('✅ التقرير يبحث عن الصور بأسماء صحيحة');
console.log('✅ النظام يُظهر حالة كل صورة (موجود/غير موجود)');
console.log(`📄 افتح التقرير: ${result.reportPath}`);

console.log('\n🚀 لاختبار كامل، افتح التقرير في المتصفح!');
