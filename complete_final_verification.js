// التحقق النهائي الشامل من جميع إصلاحات أسماء الصور وصور بعد الاستغلال

const fs = require('fs');

console.log('🔍 التحقق النهائي الشامل من جميع الإصلاحات...');
console.log('==============================================');

// قراءة الملفات
const bugBountyCode = fs.readFileSync('./assets/modules/bugbounty/BugBountyCore.js', 'utf8');
const pythonService = fs.readFileSync('./assets/modules/bugbounty/python_web_service.py', 'utf8');
const pythonBridge = fs.readFileSync('./assets/modules/bugbounty/python_screenshot_bridge.js', 'utf8');

let issues = [];
let fixes = [];

// 1. فحص أسماء الصور في Python Service
console.log('\n🐍 فحص Python Service:');

// التحقق من عدم وجود timestamp في أسماء الملفات
const timestampPattern = /filename\s*=\s*f"screenshot_.*timestamp/g;
const timestampMatches = pythonService.match(timestampPattern);

if (timestampMatches && timestampMatches.length > 0) {
    issues.push(`Python Service يستخدم timestamp في ${timestampMatches.length} مكان`);
} else {
    fixes.push('Python Service لا يستخدم timestamp في أسماء الملفات');
}

// التحقق من استخدام filename من البيانات
const filenameFromData = pythonService.includes('filename = data.get(\'filename\'');
if (filenameFromData) {
    fixes.push('Python Service يستخدم filename من البيانات');
} else {
    issues.push('Python Service لا يستخدم filename من البيانات');
}

// التحقق من إصلاح screenshot_id في v4_website
const screenshotIdUsage = pythonService.includes('screenshot_id if c.isalnum()');
if (screenshotIdUsage) {
    issues.push('Python Service لا يزال يستخدم screenshot_id في v4_website');
} else {
    fixes.push('Python Service يستخدم filename في v4_website');
}

// 2. فحص BugBountyCore.js
console.log('\n🔧 فحص BugBountyCore.js:');

// التحقق من عدم إزالة .png من اسم الملف
const removePngPattern = /\.replace\(\'\.png\',\s*\'\'\)/g;
const removePngMatches = bugBountyCode.match(removePngPattern);

if (removePngMatches && removePngMatches.length > 0) {
    issues.push(`BugBountyCore يزيل .png في ${removePngMatches.length} مكان`);
} else {
    fixes.push('BugBountyCore لا يزيل .png من أسماء الملفات');
}

// التحقق من استخدام getCorrectImageName
const getCorrectImageNameUsage = bugBountyCode.match(/getCorrectImageName/g);
const getCorrectImageNameCount = getCorrectImageNameUsage ? getCorrectImageNameUsage.length : 0;

if (getCorrectImageNameCount >= 10) {
    fixes.push(`استخدام getCorrectImageName: ${getCorrectImageNameCount} مرة`);
} else {
    issues.push(`استخدام getCorrectImageName قليل: ${getCorrectImageNameCount} مرة`);
}

// 3. فحص Python Bridge
console.log('\n🌉 فحص Python Bridge:');

const bridgeUsesFilename = pythonBridge.includes('filename: args[1]');
const bridgeNotUsingScreenshotId = !pythonBridge.includes('screenshot_id: args[1]');

if (bridgeUsesFilename && bridgeNotUsingScreenshotId) {
    fixes.push('Python Bridge يستخدم filename بدلاً من screenshot_id');
} else {
    issues.push('Python Bridge لا يزال يستخدم screenshot_id');
}

// 4. فحص دالة captureAfterExploitationScreenshot
console.log('\n🎯 فحص دالة captureAfterExploitationScreenshot:');

const hasAfterFunction = bugBountyCode.includes('captureAfterExploitationScreenshot');
const hasPayloads = bugBountyCode.includes('OR \'1\'=\'1\'') && 
                   bugBountyCode.includes('<script>alert') && 
                   bugBountyCode.includes('/api/docs');
const hasValidationFunction = bugBountyCode.includes('isValidScreenshot');
const hasDirectoryTraversal = bugBountyCode.includes('../../../etc/passwd');
const hasCommandInjection = bugBountyCode.includes('cmd=whoami');

if (hasAfterFunction) {
    fixes.push('دالة captureAfterExploitationScreenshot موجودة');
} else {
    issues.push('دالة captureAfterExploitationScreenshot مفقودة');
}

if (hasPayloads) {
    fixes.push('payloads أساسية موجودة (SQL, XSS, API)');
} else {
    issues.push('payloads أساسية مفقودة');
}

if (hasValidationFunction) {
    fixes.push('دالة isValidScreenshot موجودة للتحقق من صحة الصور');
} else {
    issues.push('دالة isValidScreenshot مفقودة');
}

if (hasDirectoryTraversal && hasCommandInjection) {
    fixes.push('payloads إضافية موجودة (Directory Traversal, Command Injection)');
} else {
    issues.push('payloads إضافية مفقودة');
}

// 5. فحص مجلد الصور
console.log('\n📁 فحص مجلد الصور:');

try {
    const screenshotsPath = './assets/modules/bugbounty/screenshots/testphp_vulnweb_com';
    const files = fs.readdirSync(screenshotsPath);
    
    // عد الصور الصحيحة
    const correctImages = files.filter(file => 
        file.startsWith('before_') || 
        file.startsWith('during_') || 
        file.startsWith('after_')
    );
    
    // عد الصور الخاطئة
    const wrongImages = files.filter(file => 
        file.startsWith('screenshot_testphp_vulnweb_com_')
    );
    
    fixes.push(`صور بأسماء صحيحة: ${correctImages.length}`);
    
    if (wrongImages.length > 0) {
        issues.push(`${wrongImages.length} صورة بأسماء خاطئة في المجلد (من التشغيلات السابقة)`);
    } else {
        fixes.push('لا توجد صور بأسماء خاطئة');
    }
    
    // فحص وجود صور API Documentation Exposure
    const apiImages = files.filter(file => file.includes('API_Documentation_Exposure'));
    if (apiImages.length > 0) {
        fixes.push(`صور API Documentation Exposure: ${apiImages.length}`);
    } else {
        issues.push('لا توجد صور API_Documentation_Exposure (ستُنتج في التشغيل القادم)');
    }
    
} catch (error) {
    issues.push('لا يمكن قراءة مجلد الصور');
}

// 6. محاكاة أسماء الصور المتوقعة
console.log('\n🧪 محاكاة أسماء الصور المتوقعة:');

const testVuln = {
    name: 'API Documentation Exposure',
    url: 'http://testphp.vulnweb.com'
};

const expectedImages = [
    'before_API_Documentation_Exposure.png',
    'during_API_Documentation_Exposure.png',
    'after_API_Documentation_Exposure.png'
];

console.log('📋 أسماء الصور المتوقعة:');
expectedImages.forEach(img => console.log(`   - ${img}`));

// النتيجة النهائية
console.log('\n🎯 النتيجة النهائية:');
console.log('==================');

console.log('\n✅ الإصلاحات المطبقة بنجاح:');
fixes.forEach((fix, index) => {
    console.log(`${index + 1}. ✅ ${fix}`);
});

if (issues.length > 0) {
    console.log('\n⚠️ مشاكل متبقية (معظمها من التشغيلات السابقة):');
    issues.forEach((issue, index) => {
        console.log(`${index + 1}. ⚠️ ${issue}`);
    });
} else {
    console.log('\n🎉 لا توجد مشاكل! جميع الإصلاحات مطبقة بنجاح!');
}

// تقييم الحالة العامة
const criticalIssues = issues.filter(issue => 
    !issue.includes('من التشغيلات السابقة') && 
    !issue.includes('ستُنتج في التشغيل القادم')
);

console.log('\n📊 تقييم الحالة العامة:');
console.log(`✅ إصلاحات مطبقة: ${fixes.length}`);
console.log(`⚠️ مشاكل متبقية: ${issues.length}`);
console.log(`🔥 مشاكل حرجة: ${criticalIssues.length}`);

if (criticalIssues.length === 0) {
    console.log('\n🎉 ممتاز! النظام جاهز تماماً!');
    console.log('✅ جميع أسماء الصور ستكون صحيحة');
    console.log('✅ صور بعد الاستغلال ستكون حقيقية مع payloads');
    console.log('✅ Python Service يدعم أسماء الملفات المخصصة');
    console.log('✅ دالة التحقق من صحة الصور موجودة');
    
    console.log('\n🔥 النظام سيُنتج الآن:');
    console.log('📁 مجلد: testphp_vulnweb_com');
    console.log('📸 صور: before_API_Documentation_Exposure.png');
    console.log('📸 صور: during_API_Documentation_Exposure.png');
    console.log('📸 صور: after_API_Documentation_Exposure.png (حقيقية مع payload)');
    
} else {
    console.log('\n❌ توجد مشاكل حرجة تحتاج إصلاح:');
    criticalIssues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue}`);
    });
}

console.log('\n🚀 الخطوة التالية: تشغيل النظام لاختبار الإصلاحات الفعلية!');
