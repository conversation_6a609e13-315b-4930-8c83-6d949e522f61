// اختبار إصلاح تسمية الصور في نظام Bug Bounty v4.0
// التحقق من أن أسماء الصور تستخدم اسم الثغرة فقط وليس اسم الموقع

console.log('🧪 بدء اختبار إصلاح تسمية الصور...');

// محاكاة BugBountyCore
class TestBugBountyCore {
    getCleanVulnerabilityName(vulnerability) {
        const vulnName = vulnerability.name || vulnerability.type || 'Unknown_Vulnerability';
        
        // تنظيف اسم الثغرة وتحويله للصيغة الصحيحة
        let cleanName = vulnName.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_');
        
        // إزالة التكرار إذا وجد
        if (cleanName.includes('_')) {
            const parts = cleanName.split('_');
            const uniqueParts = [...new Set(parts)];
            cleanName = uniqueParts.join('_');
        }

        // تطبيق تحويلات خاصة لأسماء الثغرات الشائعة
        const nameMapping = {
            'API_Documentation_Exposure': 'API_Documentation_Exposure',
            'SQL_Injection': 'SQL_Injection',
            'XSS_Cross_Site_Scripting': 'XSS_Cross_Site_Scripting',
            'XSS': 'XSS_Cross_Site_Scripting',
            'CSRF_Cross_Site_Request_Forgery': 'CSRF_Cross_Site_Request_Forgery',
            'CSRF': 'CSRF_Cross_Site_Request_Forgery',
            'Command_Injection': 'Command_Injection',
            'File_Upload_Vulnerability': 'File_Upload_Vulnerability',
            'Directory_Traversal': 'Directory_Traversal',
            'Path_Traversal': 'Directory_Traversal',
            'Authentication_Bypass': 'Authentication_Bypass',
            'Session_Management_Vulnerability': 'Session_Management_Vulnerability',
            'Information_Disclosure': 'Information_Disclosure'
        };

        return nameMapping[cleanName] || cleanName;
    }

    getCorrectFolderName(vulnerability) {
        // إنشاء اسم المجلد بناءً على الرابط (الأولوية لاسم الرابط)
        const targetUrl = vulnerability.url || vulnerability.target_url || vulnerability.location || 'http://testphp.vulnweb.com';
        const cleanUrl = targetUrl.replace(/https?:\/\//, '').replace(/[\/\?&=\.]/g, '_').replace(/[<>:"|*]/g, '');
        return cleanUrl;
    }

    getCorrectImageName(stage, vulnerability) {
        const cleanVulnName = this.getCleanVulnerabilityName(vulnerability);
        return `${stage}_${cleanVulnName}.png`;
    }

    getCorrectImagePath(vulnerability, stage) {
        const folderName = this.getCorrectFolderName(vulnerability);
        const imageName = this.getCorrectImageName(stage, vulnerability);
        return `./assets/modules/bugbounty/screenshots/${folderName}/${imageName}`;
    }
}

// اختبار الحالات المختلفة
const testCore = new TestBugBountyCore();

const testCases = [
    {
        name: 'API Documentation Exposure',
        url: 'http://testphp.vulnweb.com',
        expected: {
            folder: 'testphp_vulnweb_com',
            cleanName: 'API_Documentation_Exposure',
            beforeImage: 'before_API_Documentation_Exposure.png',
            duringImage: 'during_API_Documentation_Exposure.png',
            afterImage: 'after_API_Documentation_Exposure.png'
        }
    },
    {
        name: 'SQL Injection',
        url: 'http://testphp.vulnweb.com/artists.php',
        expected: {
            folder: 'testphp_vulnweb_com',
            cleanName: 'SQL_Injection',
            beforeImage: 'before_SQL_Injection.png',
            duringImage: 'during_SQL_Injection.png',
            afterImage: 'after_SQL_Injection.png'
        }
    },
    {
        name: 'XSS Cross Site Scripting',
        url: 'http://testphp.vulnweb.com/search.php',
        expected: {
            folder: 'testphp_vulnweb_com',
            cleanName: 'XSS_Cross_Site_Scripting',
            beforeImage: 'before_XSS_Cross_Site_Scripting.png',
            duringImage: 'during_XSS_Cross_Site_Scripting.png',
            afterImage: 'after_XSS_Cross_Site_Scripting.png'
        }
    }
];

console.log('\n📋 نتائج الاختبار:');
console.log('================');

let allTestsPassed = true;

testCases.forEach((testCase, index) => {
    console.log(`\n🧪 اختبار ${index + 1}: ${testCase.name}`);
    
    const vulnerability = { name: testCase.name, url: testCase.url };
    
    // اختبار اسم المجلد
    const folderName = testCore.getCorrectFolderName(vulnerability);
    const folderTest = folderName === testCase.expected.folder;
    console.log(`📁 اسم المجلد: ${folderName} ${folderTest ? '✅' : '❌'}`);
    if (!folderTest) allTestsPassed = false;
    
    // اختبار اسم الثغرة المنظف
    const cleanName = testCore.getCleanVulnerabilityName(vulnerability);
    const cleanNameTest = cleanName === testCase.expected.cleanName;
    console.log(`🏷️ اسم الثغرة المنظف: ${cleanName} ${cleanNameTest ? '✅' : '❌'}`);
    if (!cleanNameTest) allTestsPassed = false;
    
    // اختبار أسماء الصور
    const beforeImage = testCore.getCorrectImageName('before', vulnerability);
    const duringImage = testCore.getCorrectImageName('during', vulnerability);
    const afterImage = testCore.getCorrectImageName('after', vulnerability);
    
    const beforeTest = beforeImage === testCase.expected.beforeImage;
    const duringTest = duringImage === testCase.expected.duringImage;
    const afterTest = afterImage === testCase.expected.afterImage;
    
    console.log(`📸 صورة قبل: ${beforeImage} ${beforeTest ? '✅' : '❌'}`);
    console.log(`📸 صورة أثناء: ${duringImage} ${duringTest ? '✅' : '❌'}`);
    console.log(`📸 صورة بعد: ${afterImage} ${afterTest ? '✅' : '❌'}`);
    
    if (!beforeTest || !duringTest || !afterTest) allTestsPassed = false;
    
    // اختبار المسار الكامل
    const fullPath = testCore.getCorrectImagePath(vulnerability, 'before');
    const expectedPath = `./assets/modules/bugbounty/screenshots/${testCase.expected.folder}/${testCase.expected.beforeImage}`;
    const pathTest = fullPath === expectedPath;
    console.log(`🗂️ المسار الكامل: ${fullPath} ${pathTest ? '✅' : '❌'}`);
    if (!pathTest) allTestsPassed = false;
});

console.log('\n🎯 النتيجة النهائية:');
console.log('==================');
if (allTestsPassed) {
    console.log('✅ جميع الاختبارات نجحت! تم إصلاح تسمية الصور بنجاح.');
    console.log('📁 اسم المجلد: اسم الرابط ✅');
    console.log('📸 اسم الصور: stage_vulnerabilityName.png ✅');
} else {
    console.log('❌ بعض الاختبارات فشلت. يحتاج مراجعة إضافية.');
}

console.log('\n📝 ملاحظات مهمة:');
console.log('- اسم المجلد يجب أن يكون اسم الرابط (مثل testphp_vulnweb_com)');
console.log('- اسم الصور يجب أن يكون: before_VulnerabilityName.png');
console.log('- لا يجب استخدام اسم الموقع في اسم الصورة');
console.log('- الصور بعد الاستغلال يجب أن تكون حقيقية وليست ألوان فقط');
