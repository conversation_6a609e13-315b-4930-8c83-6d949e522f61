<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 اختبار شامل لنظام Bug Bounty v4.0</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .test-section {
            padding: 30px;
            border-bottom: 1px solid #eee;
        }
        .test-button {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .results {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        .success { border-left-color: #28a745; }
        .error { border-left-color: #dc3545; }
        .warning { border-left-color: #ffc107; }
        .log {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            width: 0%;
            transition: width 0.3s;
        }
        .image-preview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .image-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .image-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 5px;
            border: 2px solid #ddd;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار شامل لنظام Bug Bounty v4.0</h1>
            <p>اختبار الإصلاحات الجديدة لأسماء الصور والتقارير</p>
        </div>

        <div class="test-section">
            <h2>🎯 اختبار الإصلاحات</h2>
            <button class="test-button" onclick="startComprehensiveTest()">🚀 بدء الاختبار الشامل</button>
            <button class="test-button" onclick="testImageNaming()">📸 اختبار أسماء الصور</button>
            <button class="test-button" onclick="testReportGeneration()">📄 اختبار إنشاء التقارير</button>
            <button class="test-button" onclick="testPythonService()">🐍 اختبار خدمة Python</button>
            
            <div class="progress">
                <div class="progress-bar" id="progressBar"></div>
            </div>
            
            <div class="results" id="testResults">
                <h3>📊 نتائج الاختبار</h3>
                <p>اضغط على أحد الأزرار لبدء الاختبار...</p>
            </div>
            
            <div class="log" id="testLog">
                <div>🔄 جاهز للاختبار...</div>
            </div>
        </div>

        <div class="test-section">
            <h2>📊 إحصائيات النظام</h2>
            <div class="stats" id="systemStats">
                <div class="stat-card">
                    <div class="stat-number" id="vulnCount">0</div>
                    <div>الثغرات المكتشفة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="imageCount">0</div>
                    <div>الصور المُنتجة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="reportCount">0</div>
                    <div>التقارير المُنشأة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="successRate">0%</div>
                    <div>معدل النجاح</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🖼️ معاينة الصور المُنتجة</h2>
            <div class="image-preview" id="imagePreview">
                <p>سيتم عرض الصور هنا بعد الاختبار...</p>
            </div>
        </div>
    </div>

    <script src="./assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        let bugBountyCore;
        let testResults = {
            total: 0,
            passed: 0,
            failed: 0,
            vulnerabilities: [],
            images: [],
            reports: []
        };

        // تهيئة النظام
        async function initializeSystem() {
            try {
                log('🔧 تهيئة نظام Bug Bounty v4.0...');
                bugBountyCore = new BugBountyCore();
                await bugBountyCore.initialize();
                log('✅ تم تهيئة النظام بنجاح');
                return true;
            } catch (error) {
                log(`❌ فشل في تهيئة النظام: ${error.message}`);
                return false;
            }
        }

        // اختبار شامل
        async function startComprehensiveTest() {
            updateProgress(0);
            clearResults();
            log('🚀 بدء الاختبار الشامل...');

            const initialized = await initializeSystem();
            if (!initialized) return;

            updateProgress(10);

            // اختبار الموقع المستهدف
            const targetUrl = 'http://testphp.vulnweb.com';
            log(`🎯 الموقع المستهدف: ${targetUrl}`);

            try {
                // 1. اختبار الفحص الشامل
                updateProgress(20);
                log('📡 بدء الفحص الشامل...');
                
                const scanResult = await bugBountyCore.performComprehensiveScan(targetUrl);
                updateProgress(40);
                
                if (scanResult && scanResult.vulnerabilities) {
                    log(`✅ تم اكتشاف ${scanResult.vulnerabilities.length} ثغرة`);
                    testResults.vulnerabilities = scanResult.vulnerabilities;
                    updateStats();
                } else {
                    log('⚠️ لم يتم اكتشاف ثغرات');
                }

                // 2. اختبار إنشاء الصور
                updateProgress(60);
                log('📸 اختبار إنشاء الصور...');
                await testImageGeneration(scanResult.vulnerabilities || []);

                // 3. اختبار إنشاء التقارير
                updateProgress(80);
                log('📄 اختبار إنشاء التقارير...');
                await testReportCreation(scanResult);

                updateProgress(100);
                log('🎉 اكتمل الاختبار الشامل بنجاح!');
                
                displayResults();

            } catch (error) {
                log(`❌ خطأ في الاختبار الشامل: ${error.message}`);
                testResults.failed++;
            }
        }

        // اختبار إنشاء الصور
        async function testImageGeneration(vulnerabilities) {
            log('📸 اختبار إنشاء الصور...');
            
            for (let i = 0; i < Math.min(vulnerabilities.length, 3); i++) {
                const vuln = vulnerabilities[i];
                log(`📷 اختبار صور للثغرة: ${vuln.name}`);
                
                try {
                    // اختبار الأسماء الصحيحة
                    const cleanName = bugBountyCore.getCleanVulnerabilityName(vuln);
                    const folderName = bugBountyCore.getCorrectFolderName(vuln);
                    
                    log(`✅ اسم الثغرة المنظف: ${cleanName}`);
                    log(`✅ اسم المجلد: ${folderName}`);
                    
                    // محاولة التقاط الصور
                    const beforeImage = `before_${cleanName}.png`;
                    const duringImage = `during_${cleanName}.png`;
                    const afterImage = `after_${cleanName}.png`;
                    
                    testResults.images.push({
                        vulnerability: vuln.name,
                        before: beforeImage,
                        during: duringImage,
                        after: afterImage,
                        folder: folderName
                    });
                    
                    log(`📸 أسماء الصور: ${beforeImage}, ${duringImage}, ${afterImage}`);
                    
                } catch (error) {
                    log(`❌ خطأ في اختبار صور ${vuln.name}: ${error.message}`);
                }
            }
        }

        // اختبار إنشاء التقارير
        async function testReportCreation(scanResult) {
            log('📄 اختبار إنشاء التقارير...');
            
            try {
                if (bugBountyCore.reportExporter) {
                    const reportResult = await bugBountyCore.reportExporter.exportComprehensiveReport(scanResult);
                    
                    if (reportResult && reportResult.success) {
                        log('✅ تم إنشاء التقرير الرئيسي بنجاح');
                        testResults.reports.push('main_report');
                        testResults.passed++;
                    }
                    
                    const separateReport = await bugBountyCore.reportExporter.exportSeparateReport(scanResult);
                    if (separateReport && separateReport.success) {
                        log('✅ تم إنشاء التقرير المنفصل بنجاح');
                        testResults.reports.push('separate_report');
                        testResults.passed++;
                    }
                } else {
                    log('⚠️ reportExporter غير متوفر');
                }
            } catch (error) {
                log(`❌ خطأ في إنشاء التقارير: ${error.message}`);
                testResults.failed++;
            }
        }

        // اختبار أسماء الصور
        async function testImageNaming() {
            log('📸 اختبار أسماء الصور...');
            clearResults();
            
            const testVulns = [
                { name: 'API Documentation Exposure', url: 'http://testphp.vulnweb.com' },
                { name: 'SQL Injection', url: 'http://testphp.vulnweb.com/artists.php' },
                { name: 'XSS Cross Site Scripting', url: 'http://testphp.vulnweb.com/search.php' }
            ];
            
            const initialized = await initializeSystem();
            if (!initialized) return;
            
            testVulns.forEach(vuln => {
                const cleanName = bugBountyCore.getCleanVulnerabilityName(vuln);
                const folderName = bugBountyCore.getCorrectFolderName(vuln);
                const imagePath = bugBountyCore.getCorrectImagePath(vuln, 'before');
                
                log(`✅ ${vuln.name}:`);
                log(`   📁 مجلد: ${folderName}`);
                log(`   🏷️ اسم منظف: ${cleanName}`);
                log(`   📸 مسار الصورة: ${imagePath}`);
                
                testResults.passed++;
            });
            
            updateStats();
            displayResults();
        }

        // اختبار خدمة Python
        async function testPythonService() {
            log('🐍 اختبار خدمة Python...');
            clearResults();
            
            try {
                const initialized = await initializeSystem();
                if (!initialized) return;
                
                if (bugBountyCore.pythonBridge) {
                    log('✅ Python Bridge متوفر');
                    
                    const stats = await bugBountyCore.pythonBridge.getStats();
                    if (stats) {
                        log(`✅ إحصائيات Python: ${JSON.stringify(stats, null, 2)}`);
                        testResults.passed++;
                    }
                } else {
                    log('⚠️ Python Bridge غير متوفر');
                }
                
            } catch (error) {
                log(`❌ خطأ في اختبار Python: ${error.message}`);
                testResults.failed++;
            }
            
            updateStats();
            displayResults();
        }

        // دوال مساعدة
        function log(message) {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString('ar');
            logElement.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearResults() {
            document.getElementById('testLog').innerHTML = '<div>🔄 بدء اختبار جديد...</div>';
            testResults = { total: 0, passed: 0, failed: 0, vulnerabilities: [], images: [], reports: [] };
        }

        function updateProgress(percent) {
            document.getElementById('progressBar').style.width = percent + '%';
        }

        function updateStats() {
            document.getElementById('vulnCount').textContent = testResults.vulnerabilities.length;
            document.getElementById('imageCount').textContent = testResults.images.length * 3; // before, during, after
            document.getElementById('reportCount').textContent = testResults.reports.length;
            
            const total = testResults.passed + testResults.failed;
            const successRate = total > 0 ? Math.round((testResults.passed / total) * 100) : 0;
            document.getElementById('successRate').textContent = successRate + '%';
        }

        function displayResults() {
            const resultsElement = document.getElementById('testResults');
            
            let html = '<h3>📊 نتائج الاختبار النهائية</h3>';
            html += `<p><strong>✅ نجح:</strong> ${testResults.passed}</p>`;
            html += `<p><strong>❌ فشل:</strong> ${testResults.failed}</p>`;
            html += `<p><strong>🔍 ثغرات مكتشفة:</strong> ${testResults.vulnerabilities.length}</p>`;
            html += `<p><strong>📸 صور منتجة:</strong> ${testResults.images.length * 3}</p>`;
            html += `<p><strong>📄 تقارير منشأة:</strong> ${testResults.reports.length}</p>`;
            
            if (testResults.images.length > 0) {
                html += '<h4>📸 تفاصيل الصور:</h4>';
                testResults.images.forEach(img => {
                    html += `<div style="margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 5px;">`;
                    html += `<strong>${img.vulnerability}</strong><br>`;
                    html += `📁 المجلد: ${img.folder}<br>`;
                    html += `📸 الصور: ${img.before}, ${img.during}, ${img.after}`;
                    html += `</div>`;
                });
            }
            
            resultsElement.innerHTML = html;
            resultsElement.className = testResults.failed > 0 ? 'results warning' : 'results success';
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('🎯 صفحة الاختبار جاهزة');
            log('📋 اختر نوع الاختبار من الأزرار أعلاه');
        });
    </script>
</body>
</html>
