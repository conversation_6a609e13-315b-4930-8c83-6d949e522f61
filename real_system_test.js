// اختبار فعلي وحقيقي للنظام لاختبار الإصلاحات

const fs = require('fs');
const path = require('path');

console.log('🚀 بدء الاختبار الفعلي للنظام...');
console.log('=====================================');

// محاكاة تشغيل النظام
async function runRealSystemTest() {
    try {
        console.log('📋 خطوات الاختبار:');
        console.log('1. ✅ Python Server يعمل على localhost:8000');
        console.log('2. 🔄 سنقوم بمحاكاة تشغيل Bug Bounty v4.0');
        console.log('3. 📸 سنختبر التقاط الصور بأسماء صحيحة');
        console.log('4. 📄 سنختبر إنتاج التقارير');
        
        // محاكاة بيانات الثغرة
        const testVulnerability = {
            name: 'API Documentation Exposure',
            type: 'Information Disclosure',
            url: 'http://testphp.vulnweb.com',
            severity: 'Medium'
        };
        
        console.log('\n🎯 اختبار الثغرة:');
        console.log(`📌 اسم الثغرة: ${testVulnerability.name}`);
        console.log(`🔗 الرابط: ${testVulnerability.url}`);
        
        // اختبار Python Service
        console.log('\n🐍 اختبار Python Service...');
        
        try {
            const fetch = require('node-fetch');
            
            // اختبار health check
            console.log('🔍 اختبار health check...');
            const healthResponse = await fetch('http://localhost:8000/health');
            const healthData = await healthResponse.json();
            
            if (healthData.status === 'running') {
                console.log('✅ Python Service يعمل بشكل صحيح');
                console.log(`📊 Endpoints متوفرة: ${Object.keys(healthData.endpoints).length}`);
            } else {
                throw new Error('Python Service لا يعمل');
            }
            
            // اختبار التقاط صورة بالاسم الصحيح
            console.log('\n📸 اختبار التقاط صورة بالاسم الصحيح...');
            
            const screenshotRequest = {
                url: testVulnerability.url,
                filename: 'before_API_Documentation_Exposure.png',
                report_id: 'testphp_vulnweb_com'
            };
            
            console.log('📤 إرسال طلب التقاط صورة:');
            console.log(`   URL: ${screenshotRequest.url}`);
            console.log(`   Filename: ${screenshotRequest.filename}`);
            console.log(`   Report ID: ${screenshotRequest.report_id}`);
            
            const screenshotResponse = await fetch('http://localhost:8000/v4_website', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(screenshotRequest)
            });
            
            const screenshotData = await screenshotResponse.json();
            
            if (screenshotData.success) {
                console.log('✅ تم التقاط الصورة بنجاح!');
                console.log(`📁 مسار الملف: ${screenshotData.file_path}`);
                console.log(`📊 حجم البيانات: ${screenshotData.base64_data ? screenshotData.base64_data.length : 'غير متوفر'} حرف`);
                
                // التحقق من وجود الملف
                const expectedPath = path.join('screenshots', screenshotRequest.report_id, screenshotRequest.filename);
                console.log(`🔍 التحقق من وجود الملف: ${expectedPath}`);
                
                if (fs.existsSync(expectedPath)) {
                    console.log('✅ الملف موجود في المسار الصحيح!');
                    
                    const stats = fs.statSync(expectedPath);
                    console.log(`📏 حجم الملف: ${stats.size} بايت`);
                    console.log(`⏰ تاريخ الإنشاء: ${stats.birthtime}`);
                } else {
                    console.log('❌ الملف غير موجود في المسار المتوقع');
                }
                
            } else {
                console.log('❌ فشل في التقاط الصورة');
                console.log(`🔍 الخطأ: ${screenshotData.error}`);
            }
            
        } catch (fetchError) {
            console.log('❌ خطأ في الاتصال بـ Python Service:');
            console.log(`🔍 التفاصيل: ${fetchError.message}`);
            console.log('💡 تأكد من أن Python Service يعمل على localhost:8000');
        }
        
        // فحص مجلد الصور
        console.log('\n📁 فحص مجلد الصور الحالي...');
        
        const screenshotsPath = './screenshots/testphp_vulnweb_com';
        
        try {
            const files = fs.readdirSync(screenshotsPath);
            
            console.log(`📊 إجمالي الملفات: ${files.length}`);
            
            // البحث عن الصور الجديدة بأسماء صحيحة
            const correctImages = files.filter(file => 
                file.startsWith('before_') || 
                file.startsWith('during_') || 
                file.startsWith('after_')
            );
            
            const wrongImages = files.filter(file => 
                file.startsWith('screenshot_testphp_vulnweb_com_') ||
                file.startsWith('v4_screenshot_')
            );
            
            console.log(`✅ صور بأسماء صحيحة: ${correctImages.length}`);
            console.log(`❌ صور بأسماء خاطئة: ${wrongImages.length}`);
            
            if (correctImages.length > 0) {
                console.log('\n🎉 صور بأسماء صحيحة موجودة:');
                correctImages.slice(0, 5).forEach(img => {
                    console.log(`   📸 ${img}`);
                });
                
                if (correctImages.length > 5) {
                    console.log(`   ... و ${correctImages.length - 5} صورة أخرى`);
                }
            }
            
            // البحث عن أحدث الصور
            const recentFiles = files
                .map(file => ({
                    name: file,
                    time: fs.statSync(path.join(screenshotsPath, file)).mtime
                }))
                .sort((a, b) => b.time - a.time)
                .slice(0, 5);
            
            console.log('\n🕒 أحدث 5 صور:');
            recentFiles.forEach((file, index) => {
                const isCorrect = file.name.startsWith('before_') || 
                                file.name.startsWith('during_') || 
                                file.name.startsWith('after_');
                const status = isCorrect ? '✅' : '❌';
                console.log(`   ${status} ${file.name} (${file.time.toLocaleString('ar')})`);
            });
            
        } catch (dirError) {
            console.log(`❌ لا يمكن قراءة مجلد الصور: ${dirError.message}`);
        }
        
        // خلاصة النتائج
        console.log('\n📋 خلاصة نتائج الاختبار:');
        console.log('============================');
        
        console.log('✅ الإصلاحات المطبقة في الكود:');
        console.log('   - Python Service يستخدم filename من البيانات');
        console.log('   - screenshot_service.py يستخدم أسماء صحيحة');
        console.log('   - BugBountyCore يستخدم getCorrectImageName');
        console.log('   - Python Bridge يرسل filename صحيح');
        
        console.log('\n🎯 النتيجة المتوقعة:');
        console.log('   📸 before_API_Documentation_Exposure.png');
        console.log('   📸 during_API_Documentation_Exposure.png');
        console.log('   📸 after_API_Documentation_Exposure.png');
        
        console.log('\n🚀 لاختبار كامل، قم بتشغيل Bug Bounty v4.0 من الواجهة!');
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error.message);
    }
}

// تشغيل الاختبار
runRealSystemTest();
