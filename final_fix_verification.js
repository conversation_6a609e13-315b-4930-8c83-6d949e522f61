// التحقق النهائي من إصلاح مشكلة أسماء الصور

const fs = require('fs');

console.log('🔍 التحقق النهائي من إصلاح مشكلة أسماء الصور...');
console.log('================================================');

// قراءة الملفات
const bugBountyCode = fs.readFileSync('./assets/modules/bugbounty/BugBountyCore.js', 'utf8');
const pythonService = fs.readFileSync('./assets/modules/bugbounty/python_web_service.py', 'utf8');

let issues = [];

// 1. فحص Python Service
console.log('\n🐍 فحص Python Service:');

// التحقق من عدم وجود timestamp في أسماء الملفات
const timestampPattern = /filename\s*=\s*f"screenshot_.*timestamp/g;
const timestampMatches = pythonService.match(timestampPattern);

if (timestampMatches && timestampMatches.length > 0) {
    console.log(`❌ وجد ${timestampMatches.length} استخدام timestamp في أسماء الملفات`);
    issues.push('Python Service لا يزال يستخدم timestamp');
} else {
    console.log('✅ لا يوجد استخدام timestamp في أسماء الملفات');
}

// التحقق من استخدام filename من البيانات
const filenameFromData = pythonService.includes('filename = data.get(\'filename\'');
if (filenameFromData) {
    console.log('✅ Python Service يستخدم filename من البيانات');
} else {
    console.log('❌ Python Service لا يستخدم filename من البيانات');
    issues.push('Python Service لا يستخدم filename من البيانات');
}

// 2. فحص BugBountyCore.js
console.log('\n🔧 فحص BugBountyCore.js:');

// التحقق من عدم إزالة .png من اسم الملف
const removePngPattern = /\.replace\(\'\.png\',\s*\'\'\)/g;
const removePngMatches = bugBountyCode.match(removePngPattern);

if (removePngMatches && removePngMatches.length > 0) {
    console.log(`❌ وجد ${removePngMatches.length} مكان يزيل .png من اسم الملف`);
    issues.push('BugBountyCore يزيل .png من أسماء الملفات');
} else {
    console.log('✅ لا يتم إزالة .png من أسماء الملفات');
}

// التحقق من استخدام getCorrectImageName
const getCorrectImageNameUsage = bugBountyCode.match(/getCorrectImageName/g);
const getCorrectImageNameCount = getCorrectImageNameUsage ? getCorrectImageNameUsage.length : 0;

if (getCorrectImageNameCount >= 10) {
    console.log(`✅ استخدام getCorrectImageName: ${getCorrectImageNameCount} مرة`);
} else {
    console.log(`⚠️ استخدام getCorrectImageName قليل: ${getCorrectImageNameCount} مرة`);
}

// 3. فحص مجلد الصور الفعلي
console.log('\n📁 فحص مجلد الصور:');

try {
    const screenshotsPath = './assets/modules/bugbounty/screenshots/testphp_vulnweb_com';
    const files = fs.readdirSync(screenshotsPath);
    
    // عد الصور الصحيحة
    const correctImages = files.filter(file => 
        file.startsWith('before_') || 
        file.startsWith('during_') || 
        file.startsWith('after_')
    );
    
    // عد الصور الخاطئة
    const wrongImages = files.filter(file => 
        file.startsWith('screenshot_testphp_vulnweb_com_')
    );
    
    console.log(`✅ صور بأسماء صحيحة: ${correctImages.length}`);
    console.log(`❌ صور بأسماء خاطئة: ${wrongImages.length}`);
    
    if (wrongImages.length > 0) {
        issues.push(`${wrongImages.length} صورة بأسماء خاطئة في المجلد`);
    }
    
    // فحص وجود صور API Documentation Exposure
    const apiImages = files.filter(file => file.includes('API_Documentation_Exposure'));
    console.log(`🔍 صور API Documentation Exposure: ${apiImages.length}`);
    
    if (apiImages.length === 0) {
        issues.push('لا توجد صور API_Documentation_Exposure');
    } else {
        console.log('   📸 الصور الموجودة:');
        apiImages.forEach(img => console.log(`      - ${img}`));
    }
    
} catch (error) {
    console.log(`❌ خطأ في قراءة مجلد الصور: ${error.message}`);
    issues.push('لا يمكن قراءة مجلد الصور');
}

// 4. محاكاة أسماء الصور المتوقعة
console.log('\n🧪 محاكاة أسماء الصور المتوقعة:');

const testVuln = {
    name: 'API Documentation Exposure',
    url: 'http://testphp.vulnweb.com'
};

// محاكاة getCleanVulnerabilityName
function mockGetCleanVulnerabilityName(vulnerability) {
    const vulnName = vulnerability.name || vulnerability.type || 'Unknown_Vulnerability';
    let cleanName = vulnName.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_');
    
    const nameMapping = {
        'API_Documentation_Exposure': 'API_Documentation_Exposure',
        'SQL_Injection': 'SQL_Injection',
        'XSS_Cross_Site_Scripting': 'XSS_Cross_Site_Scripting'
    };
    
    return nameMapping[cleanName] || cleanName;
}

// محاكاة getCorrectImageName
function mockGetCorrectImageName(stage, vulnerability) {
    const cleanVulnName = mockGetCleanVulnerabilityName(vulnerability);
    return `${stage}_${cleanVulnName}.png`;
}

const expectedImages = [
    mockGetCorrectImageName('before', testVuln),
    mockGetCorrectImageName('during', testVuln),
    mockGetCorrectImageName('after', testVuln)
];

console.log('📋 أسماء الصور المتوقعة:');
expectedImages.forEach(img => console.log(`   - ${img}`));

// النتيجة النهائية
console.log('\n🎯 النتيجة النهائية:');
console.log('==================');

if (issues.length === 0) {
    console.log('🎉 ممتاز! جميع الإصلاحات مطبقة بنجاح!');
    console.log('✅ Python Service يستخدم filename من البيانات');
    console.log('✅ BugBountyCore لا يزيل .png من أسماء الملفات');
    console.log('✅ النظام جاهز لإنتاج صور بأسماء صحيحة');
    
    console.log('\n🔥 الآن النظام سيُنتج:');
    console.log('📁 مجلد: testphp_vulnweb_com');
    console.log('📸 صور: before_API_Documentation_Exposure.png');
    console.log('📸 صور: during_API_Documentation_Exposure.png');
    console.log('📸 صور: after_API_Documentation_Exposure.png');
    
} else {
    console.log('❌ توجد مشاكل تحتاج إصلاح:');
    issues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue}`);
    });
}

console.log('\n📋 ملخص الإصلاحات المطبقة:');
console.log('✅ إصلاح Python Service لاستخدام filename من البيانات');
console.log('✅ إصلاح BugBountyCore لعدم إزالة .png');
console.log('✅ إصلاح جميع استخدامات getCleanVulnerabilityName');
console.log('✅ إصلاح Python Bridge لإرسال filename');

console.log('\n🚀 الخطوة التالية: تشغيل النظام لاختبار الإصلاحات!');
