// اختبار النظام الحقيقي Bug Bounty v4.0 مع الإصلاحات

console.log('🚀 اختبار النظام الحقيقي Bug Bounty v4.0...');
console.log('===========================================');

async function testRealBugBountySystem() {
    try {
        console.log('📦 تحميل النظام الحقيقي...');
        
        // محاولة الوصول للنظام من النافذة العامة
        if (typeof window !== 'undefined' && window.BugBountyCore) {
            console.log('✅ تم العثور على BugBountyCore في النافذة العامة');
            
            const bugBountyInstance = new window.BugBountyCore();
            
            // تشغيل فحص حقيقي
            console.log('🔍 بدء الفحص الحقيقي...');
            const result = await bugBountyInstance.startComprehensiveScan('https://testphp.vulnweb.com');
            
            if (result && result.success) {
                console.log('✅ تم الفحص الحقيقي بنجاح!');
                console.log(`📊 تم اكتشاف ${result.vulnerabilities?.length || 0} ثغرة`);
                return true;
            } else {
                console.log('❌ فشل الفحص الحقيقي');
                return false;
            }
            
        } else {
            console.log('⚠️ لا يمكن الوصول للنظام من JavaScript');
            console.log('💡 يجب تشغيل النظام من الواجهة الرئيسية');
            
            // إرشادات للمستخدم
            console.log('\n📋 إرشادات تشغيل النظام الحقيقي:');
            console.log('1. افتح الواجهة الرئيسية للنظام');
            console.log('2. اختر "Bug Bounty v4.0"');
            console.log('3. أدخل الرابط: https://testphp.vulnweb.com');
            console.log('4. اضغط "بدء الفحص الشامل"');
            console.log('5. انتظر حتى اكتمال الفحص وإنشاء التقرير');
            
            return false;
        }
        
    } catch (error) {
        console.error('❌ خطأ في اختبار النظام الحقيقي:', error);
        return false;
    }
}

// فحص الإصلاحات المطبقة
function checkAppliedFixes() {
    console.log('\n🔍 فحص الإصلاحات المطبقة:');
    
    const fs = require('fs');
    
    try {
        // فحص ملف BugBountyCore.js
        const coreContent = fs.readFileSync('assets/modules/bugbounty/BugBountyCore.js', 'utf8');
        
        // فحص الإصلاحات
        const fixes = [
            {
                name: 'إصلاح base64 مباشر للصور',
                pattern: /data:image\/png;base64,\$\{screenshots\.before\}/,
                found: coreContent.includes('data:image/png;base64,${screenshots.before}')
            },
            {
                name: 'إصلاح معالجة خطأ الصور',
                pattern: /onerror="this\.style\.display='none';"/,
                found: coreContent.includes('onerror="this.style.display=\'none\';"')
            },
            {
                name: 'إصلاح مسارات الصور',
                pattern: /assets\/modules\/bugbounty\/screenshots/,
                found: coreContent.includes('assets/modules/bugbounty/screenshots')
            },
            {
                name: 'إصلاح دالة guessVulnerabilityType',
                pattern: /guessVulnerabilityType/,
                found: coreContent.includes('guessVulnerabilityType')
            }
        ];
        
        fixes.forEach(fix => {
            const status = fix.found ? '✅' : '❌';
            console.log(`   ${status} ${fix.name}`);
        });
        
        const appliedFixes = fixes.filter(f => f.found).length;
        console.log(`\n📊 تم تطبيق ${appliedFixes}/${fixes.length} إصلاحات`);
        
        if (appliedFixes === fixes.length) {
            console.log('🎉 جميع الإصلاحات مطبقة بنجاح!');
            return true;
        } else {
            console.log('⚠️ بعض الإصلاحات لم تُطبق بعد');
            return false;
        }
        
    } catch (error) {
        console.error('❌ خطأ في فحص الإصلاحات:', error);
        return false;
    }
}

// فحص ملفات النظام
function checkSystemFiles() {
    console.log('\n📁 فحص ملفات النظام:');
    
    const fs = require('fs');
    
    const systemFiles = [
        'assets/modules/bugbounty/BugBountyCore.js',
        'assets/modules/bugbounty/impact_visualizer.js',
        'assets/modules/bugbounty/report_exporter.js',
        'assets/modules/bugbounty/screenshot_service.py',
        'assets/modules/bugbounty/python_web_service.py'
    ];
    
    systemFiles.forEach(file => {
        const exists = fs.existsSync(file);
        const status = exists ? '✅' : '❌';
        
        if (exists) {
            const stats = fs.statSync(file);
            const sizeKB = Math.round(stats.size / 1024);
            console.log(`   ${status} ${file} (${sizeKB}KB)`);
        } else {
            console.log(`   ${status} ${file} (غير موجود)`);
        }
    });
}

// تشغيل الاختبارات
async function runAllTests() {
    console.log('🧪 بدء جميع الاختبارات...');
    
    // 1. فحص ملفات النظام
    checkSystemFiles();
    
    // 2. فحص الإصلاحات المطبقة
    const fixesApplied = checkAppliedFixes();
    
    // 3. اختبار النظام الحقيقي
    const systemWorking = await testRealBugBountySystem();
    
    // 4. النتيجة النهائية
    console.log('\n📋 النتيجة النهائية:');
    console.log('==================');
    
    if (fixesApplied && systemWorking) {
        console.log('🎉 النظام جاهز ويعمل بشكل صحيح!');
        console.log('✅ جميع الإصلاحات مطبقة');
        console.log('✅ النظام الحقيقي يعمل');
        console.log('🚀 يمكنك الآن تشغيل Bug Bounty v4.0 من الواجهة');
    } else if (fixesApplied && !systemWorking) {
        console.log('⚠️ الإصلاحات مطبقة لكن النظام يحتاج تشغيل من الواجهة');
        console.log('✅ جميع الإصلاحات مطبقة');
        console.log('💡 اتبع الإرشادات أعلاه لتشغيل النظام');
    } else {
        console.log('❌ النظام يحتاج مزيد من الإصلاحات');
        console.log('🔧 تحقق من الإصلاحات المفقودة أعلاه');
    }
    
    console.log('\n🎯 الخطوات التالية:');
    console.log('1. تشغيل النظام من الواجهة الرئيسية');
    console.log('2. اختبار فحص موقع testphp.vulnweb.com');
    console.log('3. التحقق من عرض الصور في التقرير');
    console.log('4. التأكد من عدم ظهور رسائل "فشل في تحميل الصورة"');
}

// تشغيل جميع الاختبارات
runAllTests().then(() => {
    console.log('\n🚀 اكتمل الاختبار!');
}).catch(error => {
    console.error('❌ خطأ في الاختبار:', error);
});
