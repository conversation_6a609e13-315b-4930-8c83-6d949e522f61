// اختبار نهائي للتحقق من إصلاح جميع أسماء الصور في نظام Bug Bounty v4.0

const fs = require('fs');
const path = require('path');

console.log('🔍 فحص نهائي لإصلاحات تسمية الصور...');

// قراءة ملف BugBountyCore.js
const filePath = './assets/modules/bugbounty/BugBountyCore.js';
const fileContent = fs.readFileSync(filePath, 'utf8');

// البحث عن الأنماط الخاطئة
const wrongPatterns = [
    /\.name\.replace\(\/\\s\+\/g,\s*'_'\)/g,  // vulnerability.name.replace(/\s+/g, '_')
    /before_.*\.replace.*_.*\)/g,              // before_something.replace(..., '_')
    /during_.*\.replace.*_.*\)/g,              // during_something.replace(..., '_')
    /after_.*\.replace.*_.*\)/g,               // after_something.replace(..., '_')
    /vuln\.name\.replace/g,                    // vuln.name.replace
    /vulnerability\.name\.replace/g            // vulnerability.name.replace
];

const correctPatterns = [
    /getCleanVulnerabilityName\(/g,           // استخدام الدالة الصحيحة
    /before_.*getCleanVulnerabilityName/g,    // before مع الدالة الصحيحة
    /during_.*getCleanVulnerabilityName/g,    // during مع الدالة الصحيحة
    /after_.*getCleanVulnerabilityName/g      // after مع الدالة الصحيحة
];

console.log('\n📋 نتائج الفحص:');
console.log('================');

let foundIssues = false;

// فحص الأنماط الخاطئة
console.log('\n❌ البحث عن الأنماط الخاطئة:');
wrongPatterns.forEach((pattern, index) => {
    const matches = fileContent.match(pattern);
    if (matches && matches.length > 0) {
        console.log(`❌ وجد ${matches.length} مطابقة للنمط الخاطئ ${index + 1}: ${pattern}`);
        foundIssues = true;
        
        // عرض أول 3 مطابقات كأمثلة
        matches.slice(0, 3).forEach((match, i) => {
            console.log(`   ${i + 1}. ${match}`);
        });
    } else {
        console.log(`✅ لا توجد مطابقات للنمط الخاطئ ${index + 1}`);
    }
});

// فحص الأنماط الصحيحة
console.log('\n✅ البحث عن الأنماط الصحيحة:');
correctPatterns.forEach((pattern, index) => {
    const matches = fileContent.match(pattern);
    if (matches && matches.length > 0) {
        console.log(`✅ وجد ${matches.length} مطابقة للنمط الصحيح ${index + 1}: ${pattern}`);
    } else {
        console.log(`⚠️ لا توجد مطابقات للنمط الصحيح ${index + 1}`);
    }
});

// فحص دوال التقارير الرئيسية
console.log('\n🔍 فحص دوال التقارير الرئيسية:');
const reportFunctions = [
    'generateMainReport',
    'generateSeparateReport', 
    'generateVulnerabilityImagesHTML',
    'generateScreenshotsForVulnerabilities',
    'captureRealWebsiteScreenshot'
];

reportFunctions.forEach(funcName => {
    const funcRegex = new RegExp(`${funcName}[\\s\\S]*?(?=\\n\\s{4}[a-zA-Z]|\\n\\s{0,3}\\}|$)`, 'g');
    const funcMatch = fileContent.match(funcRegex);
    
    if (funcMatch) {
        const funcContent = funcMatch[0];
        const hasWrongPattern = wrongPatterns.some(pattern => pattern.test(funcContent));
        
        if (hasWrongPattern) {
            console.log(`❌ الدالة ${funcName} تحتوي على أنماط خاطئة`);
            foundIssues = true;
        } else {
            console.log(`✅ الدالة ${funcName} تستخدم الأنماط الصحيحة`);
        }
    } else {
        console.log(`⚠️ لم يتم العثور على الدالة ${funcName}`);
    }
});

// فحص أمثلة محددة من التقارير
console.log('\n📊 فحص أمثلة محددة:');

// البحث عن مسارات الصور في التقارير
const imagePathPatterns = [
    /before_.*\.png/g,
    /during_.*\.png/g,
    /after_.*\.png/g
];

imagePathPatterns.forEach((pattern, index) => {
    const matches = fileContent.match(pattern);
    if (matches) {
        console.log(`📸 وجد ${matches.length} مسار صورة من النوع ${['before', 'during', 'after'][index]}`);
        
        // فحص أول 3 أمثلة
        const examples = matches.slice(0, 3);
        examples.forEach(example => {
            // التحقق من أن المسار لا يحتوي على اسم الموقع
            if (example.includes('_') && !example.match(/^(before|during|after)_[A-Za-z0-9_]+\.png$/)) {
                console.log(`   ⚠️ مسار مشكوك فيه: ${example}`);
                foundIssues = true;
            } else {
                console.log(`   ✅ مسار صحيح: ${example}`);
            }
        });
    }
});

// النتيجة النهائية
console.log('\n🎯 النتيجة النهائية:');
console.log('==================');

if (!foundIssues) {
    console.log('✅ ممتاز! تم إصلاح جميع أسماء الصور بنجاح');
    console.log('✅ جميع الدوال تستخدم getCleanVulnerabilityName()');
    console.log('✅ لا توجد أنماط خاطئة في الكود');
    console.log('✅ أسماء الصور تتبع النمط الصحيح: stage_VulnerabilityName.png');
    
    console.log('\n📝 ملخص الإصلاحات:');
    console.log('- ✅ اسم المجلد: اسم الرابط (مثل testphp_vulnweb_com)');
    console.log('- ✅ اسم الصور: stage_VulnerabilityName.png');
    console.log('- ✅ لا يتم استخدام اسم الموقع في اسم الصورة');
    console.log('- ✅ جميع التقارير (الرئيسي والمنفصل) تستخدم الأسماء الصحيحة');
    
} else {
    console.log('❌ تم العثور على مشاكل تحتاج إصلاح إضافي');
    console.log('❌ راجع التفاصيل أعلاه لمعرفة المشاكل المحددة');
}

console.log('\n📋 التوصيات التالية:');
console.log('1. اختبار النظام بفحص شامل');
console.log('2. التحقق من الصور المُنتجة في المجلدات');
console.log('3. مراجعة التقارير المُصدرة للتأكد من صحة المسارات');

// إحصائيات إضافية
const totalLines = fileContent.split('\n').length;
const getCleanVulnMatches = fileContent.match(/getCleanVulnerabilityName/g);
const getCleanVulnCount = getCleanVulnMatches ? getCleanVulnMatches.length : 0;

console.log('\n📊 إحصائيات:');
console.log(`📄 إجمالي الأسطر: ${totalLines}`);
console.log(`🔧 استخدامات getCleanVulnerabilityName: ${getCleanVulnCount}`);
console.log(`📸 مسارات الصور المُفحوصة: ${imagePathPatterns.reduce((sum, pattern) => {
    const matches = fileContent.match(pattern);
    return sum + (matches ? matches.length : 0);
}, 0)}`);
