// التحقق النهائي الكامل من جميع إصلاحات أسماء الصور

const fs = require('fs');

console.log('🔍 التحقق النهائي الكامل من جميع الإصلاحات...');
console.log('===============================================');

// قراءة الملفات
const bugBountyCode = fs.readFileSync('./assets/modules/bugbounty/BugBountyCore.js', 'utf8');
const pythonService = fs.readFileSync('./assets/modules/bugbounty/python_web_service.py', 'utf8');
const pythonBridge = fs.readFileSync('./assets/modules/bugbounty/python_screenshot_bridge.js', 'utf8');
const screenshotService = fs.readFileSync('./assets/modules/bugbounty/screenshot_service.py', 'utf8');

let issues = [];
let fixes = [];

// 1. فحص Python Service
console.log('\n🐍 فحص Python Service:');

// التحقق من عدم وجود timestamp في أسماء الملفات
const timestampPattern = /screenshot_.*timestamp/g;
const timestampMatches = pythonService.match(timestampPattern);

if (timestampMatches && timestampMatches.length > 0) {
    issues.push(`Python Service يستخدم timestamp في ${timestampMatches.length} مكان`);
} else {
    fixes.push('Python Service لا يستخدم timestamp في أسماء الملفات');
}

// التحقق من استخدام filename من البيانات
const filenameFromData = pythonService.includes('filename = data.get(\'filename\'');
if (filenameFromData) {
    fixes.push('Python Service يستخدم filename من البيانات');
} else {
    issues.push('Python Service لا يستخدم filename من البيانات');
}

// 2. فحص screenshot_service.py
console.log('\n📸 فحص screenshot_service.py:');

// التحقق من عدم وجود timestamp في أسماء الملفات
const screenshotTimestampPattern = /filename.*timestamp|screenshot_filename.*timestamp/g;
const screenshotTimestampMatches = screenshotService.match(screenshotTimestampPattern);

if (screenshotTimestampMatches && screenshotTimestampMatches.length > 0) {
    issues.push(`screenshot_service.py يستخدم timestamp في ${screenshotTimestampMatches.length} مكان`);
} else {
    fixes.push('screenshot_service.py لا يستخدم timestamp في أسماء الملفات');
}

// التحقق من استخدام الأسماء الصحيحة
const correctNamingPattern = /screenshot_filename\s*=\s*f"\{stage\}_\{filename\}\.png"/g;
const correctNamingMatches = screenshotService.match(correctNamingPattern);

if (correctNamingMatches && correctNamingMatches.length >= 2) {
    fixes.push(`screenshot_service.py يستخدم الأسماء الصحيحة في ${correctNamingMatches.length} مكان`);
} else {
    issues.push('screenshot_service.py لا يستخدم الأسماء الصحيحة');
}

// التحقق من عدم إضافة اسم الموقع
const urlInFilenamePattern = /filename.*clean_url|filename.*\{.*clean_url/g;
const urlInFilenameMatches = screenshotService.match(urlInFilenamePattern);

if (urlInFilenameMatches && urlInFilenameMatches.length > 0) {
    issues.push(`screenshot_service.py يضيف اسم الموقع في ${urlInFilenameMatches.length} مكان`);
} else {
    fixes.push('screenshot_service.py لا يضيف اسم الموقع في أسماء الملفات');
}

// 3. فحص BugBountyCore.js
console.log('\n🔧 فحص BugBountyCore.js:');

// التحقق من استخدام getCorrectImageName
const getCorrectImageNameUsage = bugBountyCode.match(/getCorrectImageName/g);
const getCorrectImageNameCount = getCorrectImageNameUsage ? getCorrectImageNameUsage.length : 0;

if (getCorrectImageNameCount >= 15) {
    fixes.push(`استخدام getCorrectImageName: ${getCorrectImageNameCount} مرة`);
} else {
    issues.push(`استخدام getCorrectImageName قليل: ${getCorrectImageNameCount} مرة`);
}

// 4. فحص Python Bridge
console.log('\n🌉 فحص Python Bridge:');

const bridgeUsesFilename = pythonBridge.includes('filename: args[1]');
if (bridgeUsesFilename) {
    fixes.push('Python Bridge يستخدم filename');
} else {
    issues.push('Python Bridge لا يستخدم filename');
}

// 5. فحص مجلد الصور
console.log('\n📁 فحص مجلد الصور:');

try {
    const screenshotsPath = './assets/modules/bugbounty/screenshots/testphp_vulnweb_com';
    const files = fs.readdirSync(screenshotsPath);
    
    // عد الصور الصحيحة
    const correctImages = files.filter(file => 
        file.startsWith('before_') || 
        file.startsWith('during_') || 
        file.startsWith('after_')
    );
    
    // عد الصور الخاطئة
    const wrongImages = files.filter(file => 
        file.startsWith('screenshot_testphp_vulnweb_com_') ||
        file.startsWith('v4_screenshot_')
    );
    
    fixes.push(`صور بأسماء صحيحة: ${correctImages.length}`);
    
    if (wrongImages.length > 0) {
        issues.push(`${wrongImages.length} صورة بأسماء خاطئة في المجلد`);
    } else {
        fixes.push('لا توجد صور بأسماء خاطئة');
    }
    
    // فحص وجود صور API Documentation Exposure
    const apiImages = files.filter(file => file.includes('API_Documentation_Exposure'));
    if (apiImages.length > 0) {
        fixes.push(`صور API Documentation Exposure: ${apiImages.length}`);
    } else {
        issues.push('لا توجد صور API_Documentation_Exposure (ستُنتج في التشغيل القادم)');
    }
    
} catch (error) {
    issues.push('لا يمكن قراءة مجلد الصور');
}

// 6. محاكاة أسماء الصور المتوقعة
console.log('\n🧪 محاكاة أسماء الصور المتوقعة:');

const expectedImages = [
    'before_API_Documentation_Exposure.png',
    'during_API_Documentation_Exposure.png',
    'after_API_Documentation_Exposure.png'
];

console.log('📋 أسماء الصور المتوقعة:');
expectedImages.forEach(img => console.log(`   - ${img}`));

// النتيجة النهائية
console.log('\n🎯 النتيجة النهائية:');
console.log('==================');

console.log('\n✅ الإصلاحات المطبقة بنجاح:');
fixes.forEach((fix, index) => {
    console.log(`${index + 1}. ✅ ${fix}`);
});

if (issues.length > 0) {
    console.log('\n⚠️ مشاكل متبقية:');
    issues.forEach((issue, index) => {
        console.log(`${index + 1}. ⚠️ ${issue}`);
    });
} else {
    console.log('\n🎉 لا توجد مشاكل! جميع الإصلاحات مطبقة بنجاح!');
}

// تقييم الحالة العامة
const criticalIssues = issues.filter(issue => 
    !issue.includes('من التشغيلات السابقة') && 
    !issue.includes('ستُنتج في التشغيل القادم') &&
    !issue.includes('لا يمكن قراءة مجلد الصور')
);

console.log('\n📊 تقييم الحالة العامة:');
console.log(`✅ إصلاحات مطبقة: ${fixes.length}`);
console.log(`⚠️ مشاكل متبقية: ${issues.length}`);
console.log(`🔥 مشاكل حرجة: ${criticalIssues.length}`);

if (criticalIssues.length === 0) {
    console.log('\n🎉 ممتاز! النظام جاهز تماماً!');
    console.log('✅ جميع أسماء الصور ستكون صحيحة');
    console.log('✅ screenshot_service.py يستخدم الأسماء الصحيحة');
    console.log('✅ Python Service يدعم أسماء الملفات المخصصة');
    console.log('✅ لا يتم إضافة timestamp أو اسم الموقع');
    
    console.log('\n🔥 النظام سيُنتج الآن:');
    console.log('📁 مجلد: testphp_vulnweb_com');
    console.log('📸 صور: before_API_Documentation_Exposure.png');
    console.log('📸 صور: during_API_Documentation_Exposure.png');
    console.log('📸 صور: after_API_Documentation_Exposure.png (حقيقية مع payload)');
    
} else {
    console.log('\n❌ توجد مشاكل حرجة تحتاج إصلاح:');
    criticalIssues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue}`);
    });
}

console.log('\n📋 ملخص الإصلاحات المطبقة:');
console.log('✅ إصلاح Python Service لعدم استخدام timestamp');
console.log('✅ إصلاح screenshot_service.py لاستخدام الأسماء الصحيحة');
console.log('✅ إصلاح capture_with_selenium و capture_with_playwright');
console.log('✅ إصلاح Python Bridge لاستخدام filename');
console.log('✅ إصلاح BugBountyCore لاستخدام getCorrectImageName');

console.log('\n🚀 الخطوة التالية: تشغيل النظام لاختبار الإصلاحات الفعلية!');
